import type * as Party from "partykit/server";

// Types for real-time events
interface AuthenticatedConnection extends Party.Connection {
  userId?: string;
  sessionId?: string;
  authenticated?: boolean;
}

interface BaseMessage {
  type: string;
  timestamp: number;
  userId?: string;
}

interface ChatMessage extends BaseMessage {
  type: 'chat_message';
  messageId: string;
  senderId: string;
  receiverId: string;
  content: string;
  conversationId: string;
}

interface TypingMessage extends BaseMessage {
  type: 'typing_start' | 'typing_stop';
  conversationId: string;
}

interface PresenceMessage extends BaseMessage {
  type: 'presence_update';
  status: 'online' | 'offline' | 'away';
}

interface NotificationMessage extends BaseMessage {
  type: 'notification';
  notificationId: string;
  recipientId: string;
  notificationType: 'like' | 'comment' | 'follow' | 'message' | 'fan_page_message' | 'fan_page_reply';
  senderId: string;
  metadata?: Record<string, any>;
}

interface MessageDeliveryStatus extends BaseMessage {
  type: 'message_status';
  messageId: string;
  status: 'sent' | 'delivered' | 'read';
}

type RealtimeMessage = ChatMessage | TypingMessage | PresenceMessage | NotificationMessage | MessageDeliveryStatus;

export default class Server implements Party.Server {
  private connections = new Map<string, AuthenticatedConnection>();
  private userConnections = new Map<string, Set<string>>(); // userId -> Set of connection IDs
  private typingUsers = new Map<string, Set<string>>(); // conversationId -> Set of userIds
  private presenceStatus = new Map<string, string>(); // userId -> status

  constructor(readonly room: Party.Room) {}

  async onConnect(conn: AuthenticatedConnection, ctx: Party.ConnectionContext) {
    console.log(`Connection attempt: ${conn.id} to room: ${this.room.id}`);

    // Extract authentication from URL parameters or headers
    const url = new URL(ctx.request.url);
    const userId = url.searchParams.get('userId');
    const sessionId = url.searchParams.get('sessionId');

    if (!userId || !sessionId) {
      console.log(`Unauthenticated connection rejected: ${conn.id}`);
      conn.close(1008, "Authentication required");
      return;
    }

    // TODO: Validate session with your auth system
    // For now, we'll trust the provided userId and sessionId
    conn.userId = userId;
    conn.sessionId = sessionId;
    conn.authenticated = true;

    this.connections.set(conn.id, conn);

    // Track user connections
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(conn.id);

    // Update presence status
    this.presenceStatus.set(userId, 'online');

    console.log(`Authenticated connection: ${conn.id} for user: ${userId} in room: ${this.room.id}`);

    // Send connection confirmation
    conn.send(JSON.stringify({
      type: 'connection_confirmed',
      userId,
      roomId: this.room.id,
      timestamp: Date.now()
    }));

    // Broadcast presence update to relevant users
    this.broadcastPresenceUpdate(userId, 'online');
  }

  async onMessage(message: string, sender: AuthenticatedConnection) {
    if (!sender.authenticated || !sender.userId) {
      console.log(`Unauthenticated message rejected from: ${sender.id}`);
      return;
    }

    try {
      const parsedMessage: RealtimeMessage = JSON.parse(message);
      parsedMessage.userId = sender.userId;
      parsedMessage.timestamp = Date.now();

      console.log(`Message from ${sender.userId}: ${parsedMessage.type}`);

      switch (parsedMessage.type) {
        case 'chat_message':
          await this.handleChatMessage(parsedMessage as ChatMessage, sender);
          break;
        case 'typing_start':
        case 'typing_stop':
          this.handleTypingMessage(parsedMessage as TypingMessage, sender);
          break;
        case 'presence_update':
          this.handlePresenceUpdate(parsedMessage as PresenceMessage, sender);
          break;
        case 'message_status':
          this.handleMessageStatus(parsedMessage as MessageDeliveryStatus, sender);
          break;
        default:
          console.log(`Unknown message type: ${parsedMessage.type}`);
      }
    } catch (error) {
      console.error(`Error processing message from ${sender.userId}:`, error);
      sender.send(JSON.stringify({
        type: 'error',
        message: 'Invalid message format',
        timestamp: Date.now()
      }));
    }
  }

  async onClose(conn: AuthenticatedConnection) {
    if (conn.userId) {
      console.log(`Connection closed: ${conn.id} for user: ${conn.userId}`);

      // Remove from connections
      this.connections.delete(conn.id);

      // Remove from user connections
      const userConnections = this.userConnections.get(conn.userId);
      if (userConnections) {
        userConnections.delete(conn.id);

        // If no more connections for this user, update presence
        if (userConnections.size === 0) {
          this.userConnections.delete(conn.userId);
          this.presenceStatus.set(conn.userId, 'offline');
          this.broadcastPresenceUpdate(conn.userId, 'offline');
        }
      }

      // Clean up typing indicators
      for (const [conversationId, typingUserSet] of this.typingUsers.entries()) {
        if (typingUserSet.has(conn.userId)) {
          typingUserSet.delete(conn.userId);
          this.broadcastTypingUpdate(conversationId);
        }
      }
    }
  }

  private async handleChatMessage(message: ChatMessage, sender: AuthenticatedConnection) {
    // Broadcast to receiver if they're online
    const receiverConnections = this.userConnections.get(message.receiverId);
    if (receiverConnections) {
      const messageToSend = JSON.stringify(message);
      receiverConnections.forEach(connId => {
        const conn = this.connections.get(connId);
        if (conn) {
          conn.send(messageToSend);
        }
      });
    }

    // Send delivery confirmation to sender
    sender.send(JSON.stringify({
      type: 'message_status',
      messageId: message.messageId,
      status: 'delivered',
      timestamp: Date.now()
    }));
  }

  private handleTypingMessage(message: TypingMessage, sender: AuthenticatedConnection) {
    const { conversationId, type } = message;

    if (!this.typingUsers.has(conversationId)) {
      this.typingUsers.set(conversationId, new Set());
    }

    const typingSet = this.typingUsers.get(conversationId)!;

    if (type === 'typing_start') {
      typingSet.add(sender.userId!);
    } else {
      typingSet.delete(sender.userId!);
    }

    this.broadcastTypingUpdate(conversationId);
  }

  private handlePresenceUpdate(message: PresenceMessage, sender: AuthenticatedConnection) {
    this.presenceStatus.set(sender.userId!, message.status);
    this.broadcastPresenceUpdate(sender.userId!, message.status);
  }

  private handleMessageStatus(message: MessageDeliveryStatus, sender: AuthenticatedConnection) {
    // Find the original sender of the message and notify them of status change
    // This would typically involve looking up the message in your database
    // For now, we'll broadcast to all connections of the message sender

    // In a real implementation, you'd query your database to find the original sender
    // const originalMessage = await getMessageById(message.messageId);
    // const originalSenderId = originalMessage.senderId;

    // For now, we'll just broadcast to the current user's connections
    const userConnections = this.userConnections.get(sender.userId!);
    if (userConnections) {
      const statusMessage = JSON.stringify(message);
      userConnections.forEach(connId => {
        const conn = this.connections.get(connId);
        if (conn && conn.id !== sender.id) {
          conn.send(statusMessage);
        }
      });
    }
  }

  private broadcastTypingUpdate(conversationId: string) {
    const typingUsers = Array.from(this.typingUsers.get(conversationId) || []);

    // Find all users involved in this conversation and notify them
    // In a real implementation, you'd query your database for conversation participants
    // For now, we'll broadcast to all connected users

    const typingMessage = JSON.stringify({
      type: 'typing_update',
      conversationId,
      typingUsers,
      timestamp: Date.now()
    });

    this.room.broadcast(typingMessage);
  }

  private broadcastPresenceUpdate(userId: string, status: string) {
    const presenceMessage = JSON.stringify({
      type: 'presence_update',
      userId,
      status,
      timestamp: Date.now()
    });

    // Broadcast to all connections except the user's own connections
    for (const [connId, conn] of this.connections.entries()) {
      if (conn.userId !== userId) {
        conn.send(presenceMessage);
      }
    }
  }

  // Utility method to send notification to specific user
  public sendNotificationToUser(userId: string, notification: NotificationMessage) {
    const userConnections = this.userConnections.get(userId);
    if (userConnections) {
      const notificationMessage = JSON.stringify(notification);
      userConnections.forEach(connId => {
        const conn = this.connections.get(connId);
        if (conn) {
          conn.send(notificationMessage);
        }
      });
    }
  }

  // Utility method to get online users
  public getOnlineUsers(): string[] {
    return Array.from(this.userConnections.keys());
  }

  // Utility method to check if user is online
  public isUserOnline(userId: string): boolean {
    return this.userConnections.has(userId);
  }

  // Health check endpoint
  async onRequest(req: Party.Request): Promise<Response> {
    if (req.method === "GET" && new URL(req.url).pathname === "/health") {
      return new Response(JSON.stringify({
        status: "healthy",
        room: this.room.id,
        connections: this.connections.size,
        onlineUsers: this.userConnections.size,
        timestamp: Date.now()
      }), {
        headers: { "Content-Type": "application/json" }
      });
    }

    return new Response("Not Found", { status: 404 });
  }
}

Server satisfies Party.Worker;
