"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useSession } from "next-auth/react";
import { usePartyKit, partyKitManager, type ChatMessage } from "@/lib/partykit/client";
import { v4 as uuidv4 } from "uuid";

// Feature flags
const ENABLE_REALTIME_MESSAGING = process.env.NEXT_PUBLIC_ENABLE_REALTIME_MESSAGING === "true";
const MIGRATION_PHASE = process.env.NEXT_PUBLIC_PARTYKIT_MIGRATION_PHASE || "development";

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  read: boolean;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
}

export interface Conversation {
  id: string;
  participantId: string;
  participantName: string;
  participantAvatar?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount: number;
  isOnline?: boolean;
}

export interface TypingStatus {
  conversationId: string;
  typingUsers: string[];
}

interface UseRealtimeMessagingOptions {
  fallbackToPolling?: boolean;
  pollingInterval?: number;
}

export function useRealtimeMessaging(options: UseRealtimeMessagingOptions = {}) {
  const { data: session } = useSession();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [typingStatus, setTypingStatus] = useState<TypingStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [usePolling, setUsePolling] = useState(!ENABLE_REALTIME_MESSAGING);

  // PartyKit connection for messaging
  const messagingConnection = usePartyKit("messaging", "main");
  
  // Refs for cleanup and state management
  const pollingIntervalRef = useRef<NodeJS.Timeout>();
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  const messageStatusTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Determine if we should use real-time or polling
  const shouldUseRealtime = ENABLE_REALTIME_MESSAGING && 
                           messagingConnection.connected && 
                           !usePolling &&
                           session?.user?.id;

  // Fallback to polling if real-time fails
  useEffect(() => {
    if (ENABLE_REALTIME_MESSAGING && !messagingConnection.connected && !messagingConnection.reconnecting) {
      if (options.fallbackToPolling !== false) {
        console.log("Falling back to polling due to connection issues");
        setUsePolling(true);
      }
    } else if (messagingConnection.connected) {
      setUsePolling(false);
    }
  }, [messagingConnection.connected, messagingConnection.reconnecting, options.fallbackToPolling]);

  // Fetch conversations (used by both real-time and polling)
  const fetchConversations = useCallback(async () => {
    if (!session?.user?.id) return;

    try {
      setIsLoading(true);
      const response = await fetch("/api/messages");
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch conversations`);
      }

      const data = await response.json();
      setConversations(data || []);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch conversations';
      setError(errorMessage);
      console.error('Error fetching conversations:', err);
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id]);

  // Fetch messages for a conversation (used by both real-time and polling)
  const fetchMessages = useCallback(async (conversationId: string) => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch(`/api/messages?userId=${encodeURIComponent(conversationId)}`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: Failed to fetch messages`);
      }

      const data = await response.json();
      setMessages(data || []);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch messages';
      setError(errorMessage);
      console.error('Error fetching messages:', err);
    }
  }, [session?.user?.id]);

  // Send message with real-time or fallback to API
  const sendMessage = useCallback(async (receiverId: string, content: string): Promise<boolean> => {
    if (!session?.user?.id || !content.trim()) return false;

    const messageId = uuidv4();
    const tempMessage: Message = {
      id: messageId,
      senderId: session.user.id,
      receiverId,
      content: content.trim(),
      timestamp: new Date().toISOString(),
      read: false,
      status: 'sending'
    };

    // Optimistically add message to UI
    setMessages(prev => [...prev, tempMessage]);
    setIsSending(true);

    try {
      if (shouldUseRealtime) {
        // Send via PartyKit
        const chatMessage: ChatMessage = {
          type: 'chat_message',
          messageId,
          senderId: session.user.id,
          receiverId,
          content: content.trim(),
          conversationId: receiverId, // Using receiverId as conversation ID for simplicity
          timestamp: Date.now(),
          userId: session.user.id
        };

        const sent = messagingConnection.sendMessage(chatMessage);
        
        if (sent) {
          // Update message status
          setMessages(prev => prev.map(msg => 
            msg.id === messageId ? { ...msg, status: 'sent' } : msg
          ));

          // Set timeout for delivery confirmation
          const timeout = setTimeout(() => {
            setMessages(prev => prev.map(msg => 
              msg.id === messageId ? { ...msg, status: 'delivered' } : msg
            ));
          }, 1000);
          
          messageStatusTimeouts.current.set(messageId, timeout);
        } else {
          throw new Error("Failed to send via PartyKit");
        }
      } else {
        // Fallback to API
        const response = await fetch("/api/messages", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ receiverId, content: content.trim() }),
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: Failed to send message`);
        }

        // Update message status
        setMessages(prev => prev.map(msg => 
          msg.id === messageId ? { ...msg, status: 'delivered' } : msg
        ));
      }

      // Refresh conversations to update last message
      await fetchConversations();
      return true;

    } catch (err) {
      console.error('Error sending message:', err);
      
      // Update message status to failed
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, status: 'failed' } : msg
      ));
      
      setError(err instanceof Error ? err.message : 'Failed to send message');
      return false;
    } finally {
      setIsSending(false);
    }
  }, [session?.user?.id, shouldUseRealtime, messagingConnection, fetchConversations]);

  // Typing indicators
  const startTyping = useCallback((conversationId: string) => {
    if (!shouldUseRealtime || !session?.user?.id) return;

    messagingConnection.sendMessage({
      type: 'typing_start',
      conversationId,
      userId: session.user.id,
      timestamp: Date.now()
    });

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Auto-stop typing after 3 seconds
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping(conversationId);
    }, 3000);
  }, [shouldUseRealtime, session?.user?.id, messagingConnection]);

  const stopTyping = useCallback((conversationId: string) => {
    if (!shouldUseRealtime || !session?.user?.id) return;

    messagingConnection.sendMessage({
      type: 'typing_stop',
      conversationId,
      userId: session.user.id,
      timestamp: Date.now()
    });

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = undefined;
    }
  }, [shouldUseRealtime, session?.user?.id, messagingConnection]);

  // Mark message as read
  const markAsRead = useCallback(async (messageId: string) => {
    if (shouldUseRealtime) {
      messagingConnection.sendMessage({
        type: 'message_status',
        messageId,
        status: 'read',
        timestamp: Date.now()
      });
    }

    // Update local state
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, read: true, status: 'read' } : msg
    ));
  }, [shouldUseRealtime, messagingConnection]);

  // Real-time message handlers
  useEffect(() => {
    if (!shouldUseRealtime) return;

    const unsubscribeChatMessage = messagingConnection.onMessage('chat_message', (message: ChatMessage) => {
      const newMessage: Message = {
        id: message.messageId,
        senderId: message.senderId,
        receiverId: message.receiverId,
        content: message.content,
        timestamp: new Date(message.timestamp).toISOString(),
        read: false,
        status: 'delivered'
      };

      setMessages(prev => {
        // Avoid duplicates
        if (prev.some(msg => msg.id === newMessage.id)) return prev;
        return [...prev, newMessage];
      });

      // Refresh conversations
      fetchConversations();
    });

    const unsubscribeTypingUpdate = messagingConnection.onMessage('typing_update', (message: any) => {
      setTypingStatus(prev => {
        const filtered = prev.filter(status => status.conversationId !== message.conversationId);
        if (message.typingUsers && message.typingUsers.length > 0) {
          return [...filtered, {
            conversationId: message.conversationId,
            typingUsers: message.typingUsers
          }];
        }
        return filtered;
      });
    });

    const unsubscribeMessageStatus = messagingConnection.onMessage('message_status', (message: any) => {
      setMessages(prev => prev.map(msg => 
        msg.id === message.messageId ? { ...msg, status: message.status } : msg
      ));

      // Clear timeout if we got a status update
      const timeout = messageStatusTimeouts.current.get(message.messageId);
      if (timeout) {
        clearTimeout(timeout);
        messageStatusTimeouts.current.delete(message.messageId);
      }
    });

    return () => {
      unsubscribeChatMessage();
      unsubscribeTypingUpdate();
      unsubscribeMessageStatus();
    };
  }, [shouldUseRealtime, messagingConnection, fetchConversations]);

  // Polling fallback
  useEffect(() => {
    if (!usePolling || !session?.user?.id) return;

    const interval = options.pollingInterval || 30000; // Default 30 seconds
    
    // Initial fetch
    fetchConversations();
    
    // Set up polling
    pollingIntervalRef.current = setInterval(() => {
      fetchConversations();
      if (selectedConversation) {
        fetchMessages(selectedConversation);
      }
    }, interval);

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [usePolling, session?.user?.id, selectedConversation, fetchConversations, fetchMessages, options.pollingInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Clear all message status timeouts
      for (const timeout of messageStatusTimeouts.current.values()) {
        clearTimeout(timeout);
      }
      messageStatusTimeouts.current.clear();
    };
  }, []);

  // Select conversation and fetch messages
  const selectConversation = useCallback((conversationId: string) => {
    setSelectedConversation(conversationId);
    fetchMessages(conversationId);
  }, [fetchMessages]);

  return {
    // State
    conversations,
    messages,
    selectedConversation,
    typingStatus,
    isLoading,
    isSending,
    error,
    
    // Connection info
    connected: shouldUseRealtime ? messagingConnection.connected : true,
    reconnecting: messagingConnection.reconnecting,
    latency: messagingConnection.latency,
    usingRealtime: shouldUseRealtime,
    
    // Actions
    sendMessage,
    selectConversation,
    startTyping,
    stopTyping,
    markAsRead,
    fetchConversations,
    fetchMessages,
    
    // Utilities
    getTypingUsers: (conversationId: string) => {
      const status = typingStatus.find(s => s.conversationId === conversationId);
      return status?.typingUsers || [];
    }
  };
}
