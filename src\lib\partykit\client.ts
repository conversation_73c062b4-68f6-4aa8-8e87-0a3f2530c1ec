"use client";

import PartySocket from "partysocket";
import { useSession } from "next-auth/react";
import { useCallback, useEffect, useRef, useState } from "react";

// Environment configuration
const PARTYKIT_HOST = process.env.NEXT_PUBLIC_PARTYKIT_HOST || "localhost:1999";
const ENABLE_REALTIME = process.env.NEXT_PUBLIC_ENABLE_REALTIME === "true";
const ENABLE_REALTIME_MESSAGING = process.env.NEXT_PUBLIC_ENABLE_REALTIME_MESSAGING === "true";
const ENABLE_REALTIME_NOTIFICATIONS = process.env.NEXT_PUBLIC_ENABLE_REALTIME_NOTIFICATIONS === "true";

// Types
export interface RealtimeMessage {
  type: string;
  timestamp: number;
  userId?: string;
}

export interface ChatMessage extends RealtimeMessage {
  type: 'chat_message';
  messageId: string;
  senderId: string;
  receiverId: string;
  content: string;
  conversationId: string;
}

export interface NotificationMessage extends RealtimeMessage {
  type: 'notification';
  notificationId: string;
  recipientId: string;
  notificationType: string;
  senderId: string;
  title: string;
  message: string;
  avatar?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  priority: 'low' | 'medium' | 'high';
}

export interface PresenceUpdate extends RealtimeMessage {
  type: 'presence_update';
  userId: string;
  status: 'online' | 'offline' | 'away' | 'busy';
}

export interface TypingIndicator extends RealtimeMessage {
  type: 'typing_start' | 'typing_stop' | 'typing_update';
  conversationId: string;
  typingUsers?: string[];
}

export interface ConnectionStatus {
  connected: boolean;
  reconnecting: boolean;
  error?: string;
  latency?: number;
}

// Connection manager class
export class PartyKitManager {
  private connections: Map<string, PartySocket> = new Map();
  private messageHandlers: Map<string, Set<(message: any) => void>> = new Map();
  private connectionStatusHandlers: Set<(status: ConnectionStatus) => void> = new Set();
  private userId: string | null = null;
  private sessionId: string | null = null;
  private reconnectAttempts: Map<string, number> = new Map();
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private latencyTestInterval: NodeJS.Timeout | null = null;
  private currentLatency: number | null = null;

  constructor() {
    // Start latency monitoring
    this.startLatencyMonitoring();
  }

  public initialize(userId: string, sessionId: string) {
    this.userId = userId;
    this.sessionId = sessionId;
  }

  public connect(room: string, party?: string): PartySocket | null {
    if (!ENABLE_REALTIME || !this.userId || !this.sessionId) {
      console.log("Real-time disabled or not authenticated");
      return null;
    }

    const connectionKey = `${party || 'main'}:${room}`;
    
    if (this.connections.has(connectionKey)) {
      return this.connections.get(connectionKey)!;
    }

    try {
      const socket = new PartySocket({
        host: PARTYKIT_HOST,
        room,
        party,
        query: {
          userId: this.userId,
          sessionId: this.sessionId,
        },
      });

      this.setupSocketHandlers(socket, connectionKey);
      this.connections.set(connectionKey, socket);
      
      console.log(`Connected to PartyKit room: ${connectionKey}`);
      return socket;
    } catch (error) {
      console.error(`Failed to connect to PartyKit room ${connectionKey}:`, error);
      this.notifyConnectionStatus({ connected: false, reconnecting: false, error: String(error) });
      return null;
    }
  }

  public disconnect(room: string, party?: string) {
    const connectionKey = `${party || 'main'}:${room}`;
    const socket = this.connections.get(connectionKey);
    
    if (socket) {
      socket.close();
      this.connections.delete(connectionKey);
      this.reconnectAttempts.delete(connectionKey);
      console.log(`Disconnected from PartyKit room: ${connectionKey}`);
    }
  }

  public disconnectAll() {
    for (const [key, socket] of this.connections.entries()) {
      socket.close();
    }
    this.connections.clear();
    this.reconnectAttempts.clear();
    
    if (this.latencyTestInterval) {
      clearInterval(this.latencyTestInterval);
      this.latencyTestInterval = null;
    }
  }

  public sendMessage(room: string, message: any, party?: string): boolean {
    const connectionKey = `${party || 'main'}:${room}`;
    const socket = this.connections.get(connectionKey);
    
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(message));
      return true;
    }
    
    console.warn(`Cannot send message to ${connectionKey}: not connected`);
    return false;
  }

  public onMessage(type: string, handler: (message: any) => void) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, new Set());
    }
    this.messageHandlers.get(type)!.add(handler);
  }

  public offMessage(type: string, handler: (message: any) => void) {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      handlers.delete(handler);
    }
  }

  public onConnectionStatus(handler: (status: ConnectionStatus) => void) {
    this.connectionStatusHandlers.add(handler);
  }

  public offConnectionStatus(handler: (status: ConnectionStatus) => void) {
    this.connectionStatusHandlers.delete(handler);
  }

  public getLatency(): number | null {
    return this.currentLatency;
  }

  public isConnected(room: string, party?: string): boolean {
    const connectionKey = `${party || 'main'}:${room}`;
    const socket = this.connections.get(connectionKey);
    return socket ? socket.readyState === WebSocket.OPEN : false;
  }

  private setupSocketHandlers(socket: PartySocket, connectionKey: string) {
    socket.addEventListener("open", () => {
      console.log(`PartyKit connection opened: ${connectionKey}`);
      this.reconnectAttempts.delete(connectionKey);
      this.notifyConnectionStatus({ connected: true, reconnecting: false, latency: this.currentLatency || undefined });
    });

    socket.addEventListener("message", (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error("Failed to parse PartyKit message:", error);
      }
    });

    socket.addEventListener("close", (event) => {
      console.log(`PartyKit connection closed: ${connectionKey}`, event.code, event.reason);
      this.connections.delete(connectionKey);
      
      if (event.code !== 1000) { // Not a normal closure
        this.attemptReconnect(connectionKey);
      }
    });

    socket.addEventListener("error", (error) => {
      console.error(`PartyKit connection error: ${connectionKey}`, error);
      this.notifyConnectionStatus({ connected: false, reconnecting: false, error: String(error) });
    });
  }

  private handleMessage(message: any) {
    const handlers = this.messageHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error("Error in message handler:", error);
        }
      });
    }

    // Handle latency test responses
    if (message.type === 'pong' && message.pingTime) {
      this.currentLatency = Date.now() - message.pingTime;
    }
  }

  private attemptReconnect(connectionKey: string) {
    const attempts = this.reconnectAttempts.get(connectionKey) || 0;
    
    if (attempts >= this.maxReconnectAttempts) {
      console.log(`Max reconnection attempts reached for ${connectionKey}`);
      this.notifyConnectionStatus({ connected: false, reconnecting: false, error: "Max reconnection attempts reached" });
      return;
    }

    this.reconnectAttempts.set(connectionKey, attempts + 1);
    this.notifyConnectionStatus({ connected: false, reconnecting: true });

    const delay = this.reconnectDelay * Math.pow(2, attempts); // Exponential backoff
    
    setTimeout(() => {
      console.log(`Attempting to reconnect ${connectionKey} (attempt ${attempts + 1})`);
      const [party, room] = connectionKey.split(':');
      this.connect(room, party === 'main' ? undefined : party);
    }, delay);
  }

  private notifyConnectionStatus(status: ConnectionStatus) {
    this.connectionStatusHandlers.forEach(handler => {
      try {
        handler(status);
      } catch (error) {
        console.error("Error in connection status handler:", error);
      }
    });
  }

  private startLatencyMonitoring() {
    this.latencyTestInterval = setInterval(() => {
      // Send ping to all connected sockets
      for (const [key, socket] of this.connections.entries()) {
        if (socket.readyState === WebSocket.OPEN) {
          socket.send(JSON.stringify({
            type: 'ping',
            pingTime: Date.now()
          }));
        }
      }
    }, 30000); // Test every 30 seconds
  }
}

// Global instance
export const partyKitManager = new PartyKitManager();

// React hook for PartyKit connection
export function usePartyKit(room: string, party?: string) {
  const { data: session } = useSession();
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({ connected: false, reconnecting: false });
  const socketRef = useRef<PartySocket | null>(null);

  useEffect(() => {
    if (session?.user?.id) {
      partyKitManager.initialize(session.user.id, session.user.id); // Using user ID as session ID for simplicity
      socketRef.current = partyKitManager.connect(room, party);
    }

    return () => {
      if (socketRef.current) {
        partyKitManager.disconnect(room, party);
      }
    };
  }, [session?.user?.id, room, party]);

  useEffect(() => {
    const handleConnectionStatus = (status: ConnectionStatus) => {
      setConnectionStatus(status);
    };

    partyKitManager.onConnectionStatus(handleConnectionStatus);
    return () => partyKitManager.offConnectionStatus(handleConnectionStatus);
  }, []);

  const sendMessage = useCallback((message: any) => {
    return partyKitManager.sendMessage(room, message, party);
  }, [room, party]);

  const onMessage = useCallback((type: string, handler: (message: any) => void) => {
    partyKitManager.onMessage(type, handler);
    return () => partyKitManager.offMessage(type, handler);
  }, []);

  return {
    connected: connectionStatus.connected,
    reconnecting: connectionStatus.reconnecting,
    error: connectionStatus.error,
    latency: partyKitManager.getLatency(),
    sendMessage,
    onMessage,
  };
}
