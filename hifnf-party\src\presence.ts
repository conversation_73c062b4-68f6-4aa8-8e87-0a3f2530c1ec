import type * as Party from "partykit/server";

interface AuthenticatedConnection extends Party.Connection {
  userId?: string;
  sessionId?: string;
  authenticated?: boolean;
  lastActivity?: number;
}

interface PresenceUpdate {
  type: 'presence_update';
  userId: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen?: number;
  timestamp: number;
}

interface TypingIndicator {
  type: 'typing_start' | 'typing_stop';
  userId: string;
  conversationId: string;
  timestamp: number;
}

interface ActivityPing {
  type: 'activity_ping';
  userId: string;
  timestamp: number;
}

type PresenceEvent = PresenceUpdate | TypingIndicator | ActivityPing;

interface UserPresence {
  userId: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen: number;
  connections: Set<string>;
}

export default class PresenceServer implements Party.Server {
  private connections = new Map<string, AuthenticatedConnection>();
  private userPresence = new Map<string, UserPresence>();
  private typingIndicators = new Map<string, Set<string>>(); // conversationId -> Set of userIds
  private activityTimeout = 5 * 60 * 1000; // 5 minutes for away status
  private cleanupInterval: any;

  constructor(readonly room: Party.Room) {
    // Start cleanup interval for inactive users
    this.cleanupInterval = setInterval(() => {
      this.cleanupInactiveUsers();
    }, 60000); // Check every minute
  }

  async onConnect(conn: AuthenticatedConnection, ctx: Party.ConnectionContext) {
    console.log(`Presence server connection: ${conn.id}`);
    
    const url = new URL(ctx.request.url);
    const userId = url.searchParams.get('userId');
    const sessionId = url.searchParams.get('sessionId');
    
    if (!userId || !sessionId) {
      console.log(`Unauthenticated presence connection rejected: ${conn.id}`);
      conn.close(1008, "Authentication required");
      return;
    }

    conn.userId = userId;
    conn.sessionId = sessionId;
    conn.authenticated = true;
    conn.lastActivity = Date.now();

    this.connections.set(conn.id, conn);
    
    // Update user presence
    if (!this.userPresence.has(userId)) {
      this.userPresence.set(userId, {
        userId,
        status: 'online',
        lastSeen: Date.now(),
        connections: new Set()
      });
    }
    
    const presence = this.userPresence.get(userId)!;
    presence.connections.add(conn.id);
    presence.status = 'online';
    presence.lastSeen = Date.now();

    console.log(`Presence connection authenticated: ${conn.id} for user: ${userId}`);
    
    // Send connection confirmation with current presence data
    conn.send(JSON.stringify({
      type: 'presence_connection_confirmed',
      userId,
      currentPresence: this.getAllPresence(),
      timestamp: Date.now()
    }));

    // Broadcast presence update
    this.broadcastPresenceUpdate(userId, 'online');
  }

  async onMessage(message: string, sender: AuthenticatedConnection) {
    if (!sender.authenticated || !sender.userId) {
      return;
    }

    // Update last activity
    sender.lastActivity = Date.now();
    const presence = this.userPresence.get(sender.userId);
    if (presence) {
      presence.lastSeen = Date.now();
      if (presence.status === 'away') {
        presence.status = 'online';
        this.broadcastPresenceUpdate(sender.userId, 'online');
      }
    }

    try {
      const parsedMessage: PresenceEvent = JSON.parse(message);
      
      switch (parsedMessage.type) {
        case 'presence_update':
          this.handlePresenceUpdate(parsedMessage as PresenceUpdate, sender);
          break;
        case 'typing_start':
        case 'typing_stop':
          this.handleTypingIndicator(parsedMessage as TypingIndicator, sender);
          break;
        case 'activity_ping':
          // Already handled above with lastActivity update
          break;
        default:
          console.log(`Unknown presence message type: ${parsedMessage.type}`);
      }
    } catch (error) {
      console.error(`Error processing presence message:`, error);
    }
  }

  async onClose(conn: AuthenticatedConnection) {
    if (conn.userId) {
      console.log(`Presence connection closed: ${conn.id} for user: ${conn.userId}`);
      
      this.connections.delete(conn.id);
      
      const presence = this.userPresence.get(conn.userId);
      if (presence) {
        presence.connections.delete(conn.id);
        
        // If no more connections, mark as offline
        if (presence.connections.size === 0) {
          presence.status = 'offline';
          presence.lastSeen = Date.now();
          this.broadcastPresenceUpdate(conn.userId, 'offline');
        }
      }

      // Clean up typing indicators
      for (const [conversationId, typingUsers] of this.typingIndicators.entries()) {
        if (typingUsers.has(conn.userId)) {
          typingUsers.delete(conn.userId);
          this.broadcastTypingUpdate(conversationId);
        }
      }
    }
  }

  private handlePresenceUpdate(message: PresenceUpdate, sender: AuthenticatedConnection) {
    const presence = this.userPresence.get(sender.userId!);
    if (presence) {
      presence.status = message.status;
      presence.lastSeen = Date.now();
      this.broadcastPresenceUpdate(sender.userId!, message.status);
    }
  }

  private handleTypingIndicator(message: TypingIndicator, sender: AuthenticatedConnection) {
    const { conversationId, type } = message;
    
    if (!this.typingIndicators.has(conversationId)) {
      this.typingIndicators.set(conversationId, new Set());
    }
    
    const typingUsers = this.typingIndicators.get(conversationId)!;
    
    if (type === 'typing_start') {
      typingUsers.add(sender.userId!);
    } else {
      typingUsers.delete(sender.userId!);
    }
    
    this.broadcastTypingUpdate(conversationId);
  }

  private broadcastPresenceUpdate(userId: string, status: string) {
    const presenceMessage = JSON.stringify({
      type: 'presence_update',
      userId,
      status,
      timestamp: Date.now()
    });

    // Broadcast to all connections except the user's own
    for (const [connId, conn] of this.connections.entries()) {
      if (conn.userId !== userId) {
        conn.send(presenceMessage);
      }
    }
  }

  private broadcastTypingUpdate(conversationId: string) {
    const typingUsers = Array.from(this.typingIndicators.get(conversationId) || []);
    
    const typingMessage = JSON.stringify({
      type: 'typing_update',
      conversationId,
      typingUsers,
      timestamp: Date.now()
    });

    // Broadcast to all connections
    this.room.broadcast(typingMessage);
  }

  private cleanupInactiveUsers() {
    const now = Date.now();
    
    for (const [userId, presence] of this.userPresence.entries()) {
      if (presence.status === 'online' && (now - presence.lastSeen) > this.activityTimeout) {
        presence.status = 'away';
        this.broadcastPresenceUpdate(userId, 'away');
      }
    }
  }

  private getAllPresence(): Record<string, { status: string; lastSeen: number }> {
    const result: Record<string, { status: string; lastSeen: number }> = {};
    
    for (const [userId, presence] of this.userPresence.entries()) {
      result[userId] = {
        status: presence.status,
        lastSeen: presence.lastSeen
      };
    }
    
    return result;
  }

  // Public utility methods
  public getUserPresence(userId: string): UserPresence | undefined {
    return this.userPresence.get(userId);
  }

  public isUserOnline(userId: string): boolean {
    const presence = this.userPresence.get(userId);
    return presence ? presence.status === 'online' : false;
  }

  public getOnlineUsers(): string[] {
    return Array.from(this.userPresence.entries())
      .filter(([_, presence]) => presence.status === 'online')
      .map(([userId, _]) => userId);
  }

  // Health check
  async onRequest(req: Party.Request): Promise<Response> {
    if (req.method === "GET" && new URL(req.url).pathname === "/health") {
      return new Response(JSON.stringify({
        status: "healthy",
        service: "presence",
        connections: this.connections.size,
        trackedUsers: this.userPresence.size,
        onlineUsers: this.getOnlineUsers().length,
        activeTyping: this.typingIndicators.size,
        timestamp: Date.now()
      }), {
        headers: { "Content-Type": "application/json" }
      });
    }

    return new Response("Not Found", { status: 404 });
  }
}
