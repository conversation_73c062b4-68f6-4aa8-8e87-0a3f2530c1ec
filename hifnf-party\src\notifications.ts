import type * as Party from "partykit/server";

interface AuthenticatedConnection extends Party.Connection {
  userId?: string;
  sessionId?: string;
  authenticated?: boolean;
}

interface NotificationMessage {
  type: 'notification';
  notificationId: string;
  recipientId: string;
  notificationType: 'like' | 'comment' | 'follow' | 'message' | 'fan_page_message' | 'fan_page_reply' | 'share' | 'mention';
  senderId: string;
  title: string;
  message: string;
  avatar?: string;
  actionUrl?: string;
  metadata?: Record<string, any>;
  timestamp: number;
  priority: 'low' | 'medium' | 'high';
}

interface NotificationRead {
  type: 'notification_read';
  notificationId: string;
  userId: string;
  timestamp: number;
}

interface NotificationDelete {
  type: 'notification_delete';
  notificationId: string;
  userId: string;
  timestamp: number;
}

type NotificationEvent = NotificationMessage | NotificationRead | NotificationDelete;

export default class NotificationServer implements Party.Server {
  private connections = new Map<string, AuthenticatedConnection>();
  private userConnections = new Map<string, Set<string>>(); // userId -> Set of connection IDs
  private notificationQueue = new Map<string, NotificationMessage[]>(); // userId -> queued notifications

  constructor(readonly room: Party.Room) {}

  async onConnect(conn: AuthenticatedConnection, ctx: Party.ConnectionContext) {
    console.log(`Notification server connection: ${conn.id}`);
    
    const url = new URL(ctx.request.url);
    const userId = url.searchParams.get('userId');
    const sessionId = url.searchParams.get('sessionId');
    
    if (!userId || !sessionId) {
      console.log(`Unauthenticated notification connection rejected: ${conn.id}`);
      conn.close(1008, "Authentication required");
      return;
    }

    conn.userId = userId;
    conn.sessionId = sessionId;
    conn.authenticated = true;

    this.connections.set(conn.id, conn);
    
    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set());
    }
    this.userConnections.get(userId)!.add(conn.id);

    console.log(`Notification connection authenticated: ${conn.id} for user: ${userId}`);
    
    // Send connection confirmation
    conn.send(JSON.stringify({
      type: 'notification_connection_confirmed',
      userId,
      timestamp: Date.now()
    }));

    // Send any queued notifications
    const queuedNotifications = this.notificationQueue.get(userId) || [];
    if (queuedNotifications.length > 0) {
      queuedNotifications.forEach(notification => {
        conn.send(JSON.stringify(notification));
      });
      this.notificationQueue.delete(userId);
    }
  }

  async onMessage(message: string, sender: AuthenticatedConnection) {
    if (!sender.authenticated || !sender.userId) {
      return;
    }

    try {
      const parsedMessage: NotificationEvent = JSON.parse(message);
      
      switch (parsedMessage.type) {
        case 'notification_read':
          this.handleNotificationRead(parsedMessage as NotificationRead);
          break;
        case 'notification_delete':
          this.handleNotificationDelete(parsedMessage as NotificationDelete);
          break;
        default:
          console.log(`Unknown notification message type: ${parsedMessage.type}`);
      }
    } catch (error) {
      console.error(`Error processing notification message:`, error);
    }
  }

  async onClose(conn: AuthenticatedConnection) {
    if (conn.userId) {
      this.connections.delete(conn.id);
      
      const userConnections = this.userConnections.get(conn.userId);
      if (userConnections) {
        userConnections.delete(conn.id);
        if (userConnections.size === 0) {
          this.userConnections.delete(conn.userId);
        }
      }
    }
  }

  private handleNotificationRead(message: NotificationRead) {
    // Broadcast to all user's connections to sync read status
    const userConnections = this.userConnections.get(message.userId);
    if (userConnections) {
      const readMessage = JSON.stringify(message);
      userConnections.forEach(connId => {
        const conn = this.connections.get(connId);
        if (conn) {
          conn.send(readMessage);
        }
      });
    }
  }

  private handleNotificationDelete(message: NotificationDelete) {
    // Broadcast to all user's connections to sync deletion
    const userConnections = this.userConnections.get(message.userId);
    if (userConnections) {
      const deleteMessage = JSON.stringify(message);
      userConnections.forEach(connId => {
        const conn = this.connections.get(connId);
        if (conn) {
          conn.send(deleteMessage);
        }
      });
    }
  }

  // Public method to send notification to user
  public sendNotification(notification: NotificationMessage) {
    const userConnections = this.userConnections.get(notification.recipientId);
    
    if (userConnections && userConnections.size > 0) {
      // User is online, send immediately
      const notificationMessage = JSON.stringify(notification);
      userConnections.forEach(connId => {
        const conn = this.connections.get(connId);
        if (conn) {
          conn.send(notificationMessage);
        }
      });
    } else {
      // User is offline, queue the notification
      if (!this.notificationQueue.has(notification.recipientId)) {
        this.notificationQueue.set(notification.recipientId, []);
      }
      this.notificationQueue.get(notification.recipientId)!.push(notification);
      
      // Limit queue size to prevent memory issues
      const queue = this.notificationQueue.get(notification.recipientId)!;
      if (queue.length > 50) {
        queue.shift(); // Remove oldest notification
      }
    }
  }

  // Utility methods
  public isUserOnline(userId: string): boolean {
    return this.userConnections.has(userId);
  }

  public getOnlineUsers(): string[] {
    return Array.from(this.userConnections.keys());
  }

  // Health check
  async onRequest(req: Party.Request): Promise<Response> {
    if (req.method === "GET" && new URL(req.url).pathname === "/health") {
      return new Response(JSON.stringify({
        status: "healthy",
        service: "notifications",
        connections: this.connections.size,
        onlineUsers: this.userConnections.size,
        queuedNotifications: Array.from(this.notificationQueue.values()).reduce((sum, queue) => sum + queue.length, 0),
        timestamp: Date.now()
      }), {
        headers: { "Content-Type": "application/json" }
      });
    }

    return new Response("Not Found", { status: 404 });
  }
}
